import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import crypto from 'crypto';
import { RequestSecurity, RateLimiter, SecurityLogger, SECURITY_CONFIG } from './security';

// Enhanced security headers middleware
export function securityHeaders() {
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:", "blob:"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Note: unsafe-eval needed for Vite in dev
        connectSrc: ["'self'", "https:", "wss:"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  });
}

// Rate limiting middleware
export const generalRateLimit = rateLimit({
  windowMs: SECURITY_CONFIG.RATE_LIMIT_WINDOW,
  max: SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(SECURITY_CONFIG.RATE_LIMIT_WINDOW / 1000),
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => RequestSecurity.getClientIP(req),
});

export const adminRateLimit = rateLimit({
  windowMs: SECURITY_CONFIG.RATE_LIMIT_WINDOW,
  max: SECURITY_CONFIG.ADMIN_RATE_LIMIT_MAX_REQUESTS,
  message: {
    error: 'Too many admin requests from this IP, please try again later.',
    retryAfter: Math.ceil(SECURITY_CONFIG.RATE_LIMIT_WINDOW / 1000),
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => RequestSecurity.getClientIP(req),
});

export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 900, // 15 minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => RequestSecurity.getClientIP(req),
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Input sanitization middleware
export function sanitizeInput(req: Request, res: Response, next: NextFunction) {
  // Sanitize string inputs in body
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = RequestSecurity.sanitizeInput(req.body[key]);
      }
    }
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = RequestSecurity.sanitizeInput(req.query[key] as string);
      }
    }
  }

  next();
}

// Security logging middleware
export function securityLogging(req: Request, res: Response, next: NextFunction) {
  const startTime = Date.now();
  const clientIP = RequestSecurity.getClientIP(req);
  const userAgent = RequestSecurity.getUserAgent(req);

  // Log suspicious patterns
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /vbscript:/i,  // VBScript injection
  ];

  const requestData = JSON.stringify(req.body) + JSON.stringify(req.query) + req.url;
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData));

  if (isSuspicious) {
    SecurityLogger.logSecurityEvent({
      eventType: 'SUSPICIOUS_REQUEST',
      ipAddress: clientIP,
      userAgent,
      details: {
        method: req.method,
        url: req.url,
        body: req.body,
        query: req.query,
      },
      severity: 'HIGH',
    });
  }

  // Log failed requests
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (res.statusCode >= 400) {
      SecurityLogger.logSecurityEvent({
        userId: req.session?.userId,
        eventType: 'REQUEST_ERROR',
        ipAddress: clientIP,
        userAgent,
        details: {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration,
        },
        severity: res.statusCode >= 500 ? 'HIGH' : 'MEDIUM',
      });
    }
  });

  next();
}

// Admin authentication middleware with enhanced security
export function requireAdminAuth(req: Request, res: Response, next: NextFunction) {
  const clientIP = RequestSecurity.getClientIP(req);
  
  // Check rate limiting for admin endpoints
  if (RateLimiter.isRateLimited(`admin:${clientIP}`, SECURITY_CONFIG.ADMIN_RATE_LIMIT_MAX_REQUESTS)) {
    SecurityLogger.logSecurityEvent({
      eventType: 'ADMIN_RATE_LIMIT_EXCEEDED',
      ipAddress: clientIP,
      userAgent: RequestSecurity.getUserAgent(req),
      severity: 'HIGH',
    });
    
    return res.status(429).json({ 
      message: "Too many admin requests. Please try again later.",
      retryAfter: Math.ceil(SECURITY_CONFIG.RATE_LIMIT_WINDOW / 1000),
    });
  }

  // Check session validity
  if (!req.session?.isAdmin && !req.session?.userId) {
    SecurityLogger.logSecurityEvent({
      eventType: 'UNAUTHORIZED_ADMIN_ACCESS',
      ipAddress: clientIP,
      userAgent: RequestSecurity.getUserAgent(req),
      details: { url: req.url },
      severity: 'HIGH',
    });
    
    return res.status(401).json({ message: "Admin authentication required" });
  }

  // Check session timeout for admin users
  const sessionAge = Date.now() - (req.session.lastActivity || 0);
  if (sessionAge > SECURITY_CONFIG.ADMIN_SESSION_TIMEOUT) {
    req.session.destroy((err) => {
      if (err) console.error('Session destruction error:', err);
    });
    
    return res.status(401).json({ message: "Session expired. Please login again." });
  }

  // Update last activity
  req.session.lastActivity = Date.now();
  
  next();
}

// 2FA verification middleware
export function require2FA(req: Request, res: Response, next: NextFunction) {
  if (!req.session?.twoFactorVerified) {
    return res.status(403).json({ 
      message: "Two-factor authentication required",
      requiresTwoFactor: true 
    });
  }
  next();
}

// CSRF protection middleware
export function csrfProtection(req: Request, res: Response, next: NextFunction) {
  // Skip CSRF for GET requests and public endpoints that don't require authentication
  if (req.method === 'GET') {
    return next();
  }

  // Skip CSRF for specific public endpoints
  const publicEndpoints = [
    '/api/auth/status',
    '/api/auth/register',
    '/api/auth/csrf-token',
    '/api/auth/login', // Allow login without CSRF initially
    '/api/products', // Allow public product viewing
    '/api/contacts'  // Allow contact form submissions
  ];

  // Check exact matches and startsWith matches
  const isPublicEndpoint = publicEndpoints.some(endpoint =>
    req.path === endpoint ||
    req.originalUrl === endpoint ||
    req.path.startsWith(endpoint) ||
    req.originalUrl.startsWith(endpoint)
  );

  if (isPublicEndpoint) {
    return next();
  }

  // Generate CSRF token for new sessions
  if (!req.session?.csrfToken) {
    req.session.csrfToken = crypto.randomBytes(32).toString('hex');
  }

  // Verify CSRF token for state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
    const token = req.headers['x-csrf-token'] ||
                  req.headers['csrf-token'] ||
                  req.body._csrf ||
                  req.body.csrfToken;

    if (!token || token !== req.session.csrfToken) {

      SecurityLogger.logSecurityEvent({
        userId: req.session?.userId,
        eventType: 'CSRF_TOKEN_MISMATCH',
        ipAddress: RequestSecurity.getClientIP(req),
        userAgent: RequestSecurity.getUserAgent(req),
        details: {
          url: req.url,
          method: req.method,
          hasToken: !!token,
          tokenMatch: token === req.session.csrfToken
        },
        severity: 'HIGH',
      });

      return res.status(403).json({
        message: "Invalid CSRF token",
        code: "CSRF_TOKEN_INVALID"
      });
    }
  }

  next();
}

// IP whitelist middleware for admin endpoints (optional)
export function adminIPWhitelist(allowedIPs: string[] = []) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (allowedIPs.length === 0) {
      return next(); // No IP restrictions
    }

    const clientIP = RequestSecurity.getClientIP(req);
    
    if (!allowedIPs.includes(clientIP)) {
      SecurityLogger.logSecurityEvent({
        eventType: 'ADMIN_IP_BLOCKED',
        ipAddress: clientIP,
        userAgent: RequestSecurity.getUserAgent(req),
        details: { allowedIPs },
        severity: 'CRITICAL',
      });
      
      return res.status(403).json({ message: "Access denied from this IP address" });
    }

    next();
  };
}
