import { Express, Request, Response } from 'express';
import crypto from 'crypto';
import {
  PasswordUtils,
  TwoFactorAuth,
  SecurityLogger,
  RequestSecurity,
  RateLimiter,
  SECURITY_CONFIG
} from './security';
import {
  authRateLimit,
  sanitizeInput,
  securityLogging,
  csrfProtection
} from './security-middleware';
import { storage } from './index';

// Extend session type
declare module 'express-session' {
  interface SessionData {
    userId?: number;
    isAdmin?: boolean;
    twoFactorVerified?: boolean;
    pendingTwoFactor?: boolean;
    lastActivity?: number;
    csrfToken?: string;
  }
}

export function setupAuthRoutes(app: Express) {
  // Apply security middleware to all auth routes
  app.use('/api/auth', authRateLimit);
  app.use('/api/auth', sanitizeInput);
  app.use('/api/auth', securityLogging);

  // CSRF token endpoint - must be before CSRF protection
  app.get('/api/auth/csrf-token', (req: Request, res: Response) => {
    if (!req.session.csrfToken) {
      req.session.csrfToken = crypto.randomBytes(32).toString('hex');
    }
    res.json({ csrfToken: req.session.csrfToken });
  });

  // Enhanced login endpoint
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    const { username, password } = req.body;
    const clientIP = RequestSecurity.getClientIP(req);
    const userAgent = RequestSecurity.getUserAgent(req);

    try {
      // Input validation
      if (!username || !password) {
        return res.status(400).json({ message: 'Username and password are required' });
      }

      // Check rate limiting per IP
      const rateLimitKey = `login:${clientIP}`;
      if (RateLimiter.isRateLimited(rateLimitKey, 10)) { // 10 attempts per 15 minutes
        await SecurityLogger.logSecurityEvent({
          eventType: 'LOGIN_RATE_LIMIT_EXCEEDED',
          ipAddress: clientIP,
          userAgent,
          details: { username },
          severity: 'HIGH',
        });
        
        return res.status(429).json({ 
          message: 'Too many login attempts. Please try again later.',
          retryAfter: Math.ceil(SECURITY_CONFIG.RATE_LIMIT_WINDOW / 1000),
        });
      }

      // Find user
      const user = await storage.getUserByUsername(username);
      if (!user) {
        await SecurityLogger.logLoginAttempt(username, clientIP, false, userAgent);
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Check if account is locked
      if (user.lockedUntil && new Date() < user.lockedUntil) {
        await SecurityLogger.logSecurityEvent({
          userId: user.id,
          eventType: 'LOGIN_ATTEMPT_LOCKED_ACCOUNT',
          ipAddress: clientIP,
          userAgent,
          details: { username },
          severity: 'MEDIUM',
        });
        
        return res.status(423).json({ 
          message: 'Account is temporarily locked due to multiple failed login attempts',
          lockedUntil: user.lockedUntil,
        });
      }

      // Verify password
      const isValidPassword = await PasswordUtils.verify(password, user.password);
      if (!isValidPassword) {
        // Increment login attempts
        const newAttempts = (user.loginAttempts || 0) + 1;
        const shouldLock = newAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS;
        
        await storage.updateUserLoginAttempts(user.id, newAttempts, shouldLock);
        await SecurityLogger.logLoginAttempt(username, clientIP, false, userAgent);
        
        if (shouldLock) {
          await SecurityLogger.logSecurityEvent({
            userId: user.id,
            eventType: 'ACCOUNT_LOCKED',
            ipAddress: clientIP,
            userAgent,
            details: { username, attempts: newAttempts },
            severity: 'HIGH',
          });
        }
        
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Reset login attempts on successful password verification
      if (user.loginAttempts > 0) {
        await storage.updateUserLoginAttempts(user.id, 0, false);
      }

      // Check if 2FA is enabled for admin users
      if (user.role === 'admin' && user.twoFactorEnabled) {
        // Set pending 2FA state
        req.session.userId = user.id;
        req.session.isAdmin = true;
        req.session.pendingTwoFactor = true;
        req.session.twoFactorVerified = false;
        req.session.lastActivity = Date.now();

        await SecurityLogger.logSecurityEvent({
          userId: user.id,
          eventType: 'LOGIN_PENDING_2FA',
          ipAddress: clientIP,
          userAgent,
          severity: 'MEDIUM',
        });

        return res.json({
          message: 'Password verified. Two-factor authentication required.',
          requiresTwoFactor: true,
          userId: user.id,
        });
      }

      // Complete login for non-2FA users
      req.session.userId = user.id;
      req.session.isAdmin = user.role === 'admin';
      req.session.twoFactorVerified = true; // Not required for this user
      req.session.pendingTwoFactor = false;
      req.session.lastActivity = Date.now();

      // Update last login
      await storage.updateUserLastLogin(user.id);
      await SecurityLogger.logLoginAttempt(username, clientIP, true, userAgent);

      // Reset rate limiting on successful login
      RateLimiter.reset(rateLimitKey);

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
      });

    } catch (error) {
      console.error('Login error:', error);
      await SecurityLogger.logSecurityEvent({
        eventType: 'LOGIN_ERROR',
        ipAddress: clientIP,
        userAgent,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'HIGH',
      });
      
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // 2FA verification endpoint
  app.post('/api/auth/verify-2fa', async (req: Request, res: Response) => {
    const { token, backupCode } = req.body;
    const clientIP = RequestSecurity.getClientIP(req);
    const userAgent = RequestSecurity.getUserAgent(req);

    try {
      if (!req.session.pendingTwoFactor || !req.session.userId) {
        return res.status(400).json({ message: 'No pending two-factor authentication' });
      }

      const user = await storage.getUserById(req.session.userId);
      if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
        return res.status(400).json({ message: 'Two-factor authentication not properly configured' });
      }

      let isValid = false;
      let usedBackupCode = false;

      // Verify TOTP token
      if (token) {
        isValid = TwoFactorAuth.verifyToken(user.twoFactorSecret, token);
      }
      // Verify backup code
      else if (backupCode && user.twoFactorBackupCodes) {
        isValid = TwoFactorAuth.verifyBackupCode(user.twoFactorBackupCodes, backupCode);
        usedBackupCode = isValid;
      }

      if (!isValid) {
        await SecurityLogger.log2FAEvent(user.id, '2FA_FAILED', clientIP);
        return res.status(401).json({ message: 'Invalid two-factor authentication code' });
      }

      // Remove used backup code
      if (usedBackupCode && user.twoFactorBackupCodes) {
        const updatedCodes = TwoFactorAuth.removeUsedBackupCode(user.twoFactorBackupCodes, backupCode);
        await storage.updateUserBackupCodes(user.id, updatedCodes);
      }

      // Complete authentication
      req.session.twoFactorVerified = true;
      req.session.pendingTwoFactor = false;
      req.session.lastActivity = Date.now();

      // Update last login
      await storage.updateUserLastLogin(user.id);
      await SecurityLogger.log2FAEvent(user.id, '2FA_VERIFIED', clientIP);

      res.json({
        message: 'Two-factor authentication successful',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        },
      });

    } catch (error) {
      console.error('2FA verification error:', error);
      await SecurityLogger.logSecurityEvent({
        userId: req.session.userId,
        eventType: '2FA_ERROR',
        ipAddress: clientIP,
        userAgent,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        severity: 'HIGH',
      });
      
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Setup 2FA endpoint (for admin users)
  app.post('/api/auth/setup-2fa', async (req: Request, res: Response) => {
    const clientIP = RequestSecurity.getClientIP(req);

    try {
      if (!req.session.userId || !req.session.isAdmin) {
        return res.status(401).json({ message: 'Admin authentication required' });
      }

      const user = await storage.getUserById(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      if (user.twoFactorEnabled) {
        return res.status(400).json({ message: 'Two-factor authentication is already enabled' });
      }

      // Generate 2FA secret and QR code
      const { secret, qrCodeUrl } = TwoFactorAuth.generateSecret(user.username);
      const qrCodeDataUrl = await TwoFactorAuth.generateQRCode(qrCodeUrl);
      const backupCodes = TwoFactorAuth.generateBackupCodes();

      // Store the secret temporarily (not enabled yet)
      await storage.updateUser2FASecret(user.id, secret);

      await SecurityLogger.logSecurityEvent({
        userId: user.id,
        eventType: '2FA_SETUP_INITIATED',
        ipAddress: clientIP,
        severity: 'MEDIUM',
      });

      res.json({
        qrCode: qrCodeDataUrl,
        manualEntryKey: secret,
        backupCodes,
      });

    } catch (error) {
      console.error('2FA setup error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Enable 2FA endpoint
  app.post('/api/auth/enable-2fa', async (req: Request, res: Response) => {
    const { token } = req.body;
    const clientIP = RequestSecurity.getClientIP(req);

    try {
      if (!req.session.userId || !req.session.isAdmin) {
        return res.status(401).json({ message: 'Admin authentication required' });
      }

      const user = await storage.getUserById(req.session.userId);
      if (!user || !user.twoFactorSecret) {
        return res.status(400).json({ message: 'Two-factor authentication setup not initiated' });
      }

      // Verify the token to ensure the user can generate valid codes
      const isValid = TwoFactorAuth.verifyToken(user.twoFactorSecret, token);
      if (!isValid) {
        return res.status(401).json({ message: 'Invalid verification code' });
      }

      // Generate backup codes
      const backupCodes = TwoFactorAuth.generateBackupCodes();

      // Enable 2FA
      await storage.enable2FA(user.id, backupCodes);
      await SecurityLogger.log2FAEvent(user.id, '2FA_ENABLED', clientIP);

      res.json({
        message: 'Two-factor authentication enabled successfully',
        backupCodes,
      });

    } catch (error) {
      console.error('2FA enable error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Logout endpoint
  app.post('/api/auth/logout', (req: Request, res: Response) => {
    const clientIP = RequestSecurity.getClientIP(req);
    const userId = req.session.userId;

    req.session.destroy((err) => {
      if (err) {
        console.error('Session destruction error:', err);
        return res.status(500).json({ message: 'Logout failed' });
      }

      if (userId) {
        SecurityLogger.logSecurityEvent({
          userId,
          eventType: 'LOGOUT',
          ipAddress: clientIP,
          severity: 'LOW',
        });
      }

      res.json({ message: 'Logout successful' });
    });
  });

  // Auth status endpoint
  app.get('/api/auth/status', async (req: Request, res: Response) => {
    if (!req.session.userId) {
      return res.json({ authenticated: false });
    }

    // Check session timeout
    const sessionAge = Date.now() - (req.session.lastActivity || 0);
    const maxAge = req.session.isAdmin ? SECURITY_CONFIG.ADMIN_SESSION_TIMEOUT : SECURITY_CONFIG.SESSION_TIMEOUT;

    if (sessionAge > maxAge) {
      req.session.destroy(() => {});
      return res.json({ authenticated: false, reason: 'Session expired' });
    }

    // Update last activity
    req.session.lastActivity = Date.now();

    try {
      // Get user data to include 2FA enabled status from database
      const user = await storage.getUserById(req.session.userId);

      res.json({
        authenticated: true,
        isAdmin: req.session.isAdmin || false,
        twoFactorVerified: req.session.twoFactorVerified || false,
        pendingTwoFactor: req.session.pendingTwoFactor || false,
        twoFactorEnabled: user?.twoFactorEnabled || false, // Database 2FA enabled status
        csrfToken: req.session.csrfToken,
        user: user ? {
          id: user.id,
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        } : null,
      });
    } catch (error) {
      console.error('Auth status error:', error);
      res.json({
        authenticated: true,
        isAdmin: req.session.isAdmin || false,
        twoFactorVerified: req.session.twoFactorVerified || false,
        pendingTwoFactor: req.session.pendingTwoFactor || false,
        twoFactorEnabled: false,
        csrfToken: req.session.csrfToken,
      });
    }
  });

  // Change password endpoint
  app.post('/api/auth/change-password', csrfProtection, async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated
      if (!req.session.userId) {
        return res.status(401).json({ message: 'Not authenticated' });
      }

      const { currentPassword, newPassword } = req.body;

      // Validate input
      if (!currentPassword || !newPassword) {
        return res.status(400).json({ message: 'Current password and new password are required' });
      }

      // Get user from database
      const user = await storage.getUserById(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify current password
      const isCurrentPasswordValid = await PasswordUtils.verify(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        await SecurityLogger.logSecurityEvent({
          userId: user.id,
          eventType: 'PASSWORD_CHANGE_FAILED',
          ipAddress: RequestSecurity.getClientIP(req),
          userAgent: req.get('User-Agent') || 'Unknown',
          details: { reason: 'Invalid current password' },
          severity: 'MEDIUM',
        });
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      // Validate new password strength
      const passwordValidation = PasswordUtils.validateStrength(newPassword);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          message: 'New password does not meet security requirements',
          errors: passwordValidation.errors
        });
      }

      // Update password in database
      await storage.updateUserPassword(user.id, newPassword);

      // Log successful password change
      await SecurityLogger.logSecurityEvent({
        userId: user.id,
        eventType: 'PASSWORD_CHANGED',
        ipAddress: RequestSecurity.getClientIP(req),
        userAgent: req.get('User-Agent') || 'Unknown',
        details: { username: user.username },
        severity: 'LOW',
      });

      res.json({ message: 'Password changed successfully' });
    } catch (error) {
      console.error('Password change error:', error);
      res.status(500).json({ message: 'Failed to change password' });
    }
  });
}
