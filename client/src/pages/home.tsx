import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { ProductCard } from "@/components/product-card";
import { Leaf, Recycle, Award, Shield, Star, CheckCircle } from "lucide-react";
import { useLocation } from "wouter";
import type { Product } from "@shared/schema";

export default function Home() {
  const [, setLocation] = useLocation();

  const { data: featuredProducts = [] } = useQuery<Product[]>({
    queryKey: ["/api/products", { featured: "true" }],
  });

  const handleShopNow = () => {
    setLocation("/products");
  };

  const handleLearnMore = () => {
    setLocation("/about");
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-green-50 via-white to-emerald-50 text-gray-800 py-20 overflow-hidden">
        {/* Subtle Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-100 rounded-full opacity-10"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-emerald-100 rounded-full opacity-10"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              {/* Plastic-Free Badge */}
              <div className="inline-flex items-center bg-gradient-to-r from-green-100 to-emerald-100 rounded-full px-8 py-4 border-2 border-green-300 shadow-lg">
                <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                <span className="text-base font-semibold text-green-800">100% Plastic-Free Products</span>
              </div>

              <div className="flex items-center space-x-6 mb-6">
                <div className="relative">
                  <div className="absolute inset-0 bg-green-200 rounded-full blur-xl opacity-20"></div>
                </div>
                <h1 className="text-5xl lg:text-7xl font-bold font-playfair leading-tight bg-gradient-to-r from-green-700 to-emerald-600 bg-clip-text text-transparent">
                  EcoGrovea
                </h1>
              </div>
              <div className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                <span className="block text-3xl lg:text-4xl font-bold font-playfair">
                  A Natural Haven For Sustainable Dining
                </span>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-green-100">
                <p className="text-xl lg:text-2xl text-gray-700 leading-relaxed">
                  Discover premium <strong className="text-green-600 font-bold">certified</strong>, <strong className="text-green-600 font-bold">compostable</strong>, <strong className="text-green-600 font-bold">natural</strong>, and <strong className="text-green-600 font-bold">durable</strong> dining solutions that protect our planet while serving your needs.
                </p>
              </div>

              {/* Key Features */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3 bg-gradient-to-r from-yellow-50 to-yellow-100 border-2 border-yellow-200 rounded-xl p-4 shadow-md">
                  <Award className="h-7 w-7 text-yellow-600 flex-shrink-0" />
                  <span className="text-lg font-semibold text-gray-800">Certified</span>
                </div>
                <div className="flex items-center space-x-3 bg-gradient-to-r from-green-50 to-green-100 border-2 border-green-200 rounded-xl p-4 shadow-md">
                  <Recycle className="h-7 w-7 text-green-600 flex-shrink-0" />
                  <span className="text-lg font-semibold text-gray-800">Compostable</span>
                </div>
                <div className="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-blue-100 border-2 border-blue-200 rounded-xl p-4 shadow-md">
                  <Leaf className="h-7 w-7 text-blue-600 flex-shrink-0" />
                  <span className="text-lg font-semibold text-gray-800">Natural</span>
                </div>
                <div className="flex items-center space-x-3 bg-gradient-to-r from-orange-50 to-orange-100 border-2 border-orange-200 rounded-xl p-4 shadow-md">
                  <Shield className="h-7 w-7 text-orange-600 flex-shrink-0" />
                  <span className="text-lg font-semibold text-gray-800">Durable</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 pt-4">
                <Button
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-12 py-5 text-xl font-bold rounded-full shadow-lg"
                  onClick={handleShopNow}
                >
                  Shop a Greener Future
                </Button>
                <Button
                  variant="outline"
                  className="border-2 border-green-600 text-green-600 hover:bg-gradient-to-r hover:from-green-600 hover:to-emerald-600 hover:text-white px-12 py-5 text-xl font-bold rounded-full shadow-md"
                  onClick={handleLearnMore}
                >
                  Our Story
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="relative rounded-3xl overflow-hidden shadow-xl">
                <div className="absolute inset-0 bg-gradient-to-t from-green-600/10 via-transparent to-emerald-600/10 z-10"></div>
                <img
                  src="https://res.cloudinary.com/dlaz5xqrl/image/upload/v1752412515/Untitled_design_3_tjzoj8.png"
                  alt="EcoGrovea sustainable dining products - plastic-free plates and containers"
                  className="w-full h-[500px] object-cover"
                />
              </div>

              {/* Product Feature Cards */}
              <div className="absolute -top-6 -left-6 bg-white border-2 border-green-200 p-4 rounded-xl shadow-lg">
                <div className="flex items-center space-x-3">
                  <div>
                    <div className="text-sm font-bold text-gray-800">Natural</div>
                    <div className="text-xs text-green-600 font-medium">Plant-Based</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white border-2 border-blue-200 p-4 rounded-xl shadow-lg">
                <div className="flex flex-col items-center space-y-1">
                   <CheckCircle className="h-6 w-6 text-emerald-600" />
                  <div className="text-xs font-bold text-gray-800">100%</div>
                  <div className="text-xs text-emerald-600 font-medium">Eco-Friendly</div>
                  {/* <Award className="h-6 w-6 text-blue-600" />
                  <div>
                    <div className="text-sm font-bold text-gray-800">Certified</div>
                    <div className="text-xs text-blue-600 font-medium">Sustainability</div>
                  </div> */}
                </div>
              </div>

              {/* <div className="absolute top-1/2 -right-8 bg-white border-2 border-emerald-200 p-4 rounded-xl shadow-lg">
                <div className="flex flex-col items-center space-y-1">
                  <CheckCircle className="h-6 w-6 text-emerald-600" />
                  <div className="text-xs font-bold text-gray-800">100%</div>
                  <div className="text-xs text-emerald-600 font-medium">Eco-Friendly</div>
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-12 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-32 h-32 bg-green-200 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-200 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-purple-200 rounded-full blur-3xl"></div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-10">
            <div className="inline-flex items-center bg-gradient-to-r from-green-100 to-emerald-100 border-2 border-green-200 rounded-full px-8 py-3 mb-6 shadow-lg hover:shadow-xl transition-all duration-300">
              <Leaf className="h-6 w-6 text-green-600 mr-3 animate-pulse" />
              <span className="text-green-700 font-bold text-lg">Sustainable Collection</span>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4 font-playfair">Featured Eco-Products</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our most popular <strong>certified</strong>, <strong>compostable</strong>, <strong>natural</strong>, and <strong>durable</strong> dining solutions.
            </p>
          </div>

          {/* Product Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {featuredProducts.slice(0, 4).map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-8">
            <div className="bg-gradient-to-r from-orange-100 via-amber-50 to-yellow-100 border-2 border-orange-200 rounded-3xl p-8 mb-4 shadow-xl hover:shadow-2xl transition-all duration-300 relative overflow-hidden">
              {/* Decorative Elements */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-2 right-2 text-4xl">🎯</div>
                <div className="absolute bottom-2 left-2 text-3xl">🚀</div>
              </div>

              <div className="relative z-10">
                <h3 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent mb-4">Ready to Go Plastic-Free?</h3>
                <p className="text-gray-700 mb-6 max-w-2xl mx-auto text-lg leading-relaxed">
                  Join us in making the switch to sustainable dining solutions. Every product comes with our quality guarantee.
                </p>
                <div className="flex flex-col sm:flex-row gap-6 justify-center">
                  <Button
                    className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-12 py-4 text-lg font-bold rounded-full shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300"
                    onClick={handleShopNow}
                  >
                    🛒 Shop All Products
                  </Button>
                  <Button
                    variant="outline"
                    className="border-3 border-orange-500 text-orange-600 hover:bg-gradient-to-r hover:from-orange-500 hover:to-amber-500 hover:text-white px-12 py-4 text-lg font-bold rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
                    onClick={handleLearnMore}
                  >
                    🌱 Learn About Sustainability
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-8 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-4xl font-bold text-gray-900 mb-3 font-playfair">Why Choose EcoGrovea?</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our commitment to sustainability goes beyond products. We deliver <strong>certified quality</strong>, <strong>natural materials</strong>, <strong>compostable solutions</strong>, and <strong>durable performance</strong> in every item.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Certified */}
            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl">
                <Award className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Certified Quality</h3>
              <p className="text-gray-600 leading-relaxed">
                Our products are rigorously tested and certified by leading sustainability organizations.
              </p>
              <div className="mt-4 flex justify-center">
                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                  Sustainability Certified
                </Badge>
              </div>
            </div>

            {/* Compostable */}
            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl">
                <Recycle className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Fully Compostable</h3>
              <p className="text-gray-600 leading-relaxed">
                Our products break down into nutrient-rich compost, leaving no trace behind.
              </p>
              <div className="mt-4 flex justify-center">
                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                  Fully Biodegradable
                </Badge>
              </div>
            </div>

            {/* Natural */}
            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl">
                <Leaf className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">All-Natural Materials</h3>
              <p className="text-gray-600 leading-relaxed">
                Made from renewable plant fibers, sugarcane bagasse, and wheat straw - completely free from plastic and chemicals.
              </p>
              <div className="mt-4 flex justify-center">
                <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                  Plant-Based
                </Badge>
              </div>
            </div>

            {/* Durable */}
            <div className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl">
                <Shield className="h-12 w-12 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Durable</h3>
              <p className="text-gray-600 leading-relaxed">
                Our dinnerware is strong, leak-proof, and heat-resistant for any meal.
              </p>
              <div className="mt-4 flex justify-center">
                <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                  Heavy-Duty
                </Badge>
              </div>
            </div>
          </div>

          {/* Plastic-Free Promise */}
          <div className="bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 rounded-3xl p-6 md:p-8 text-center shadow-2xl relative overflow-hidden">
            {/* Decorative Elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-4 right-4 text-6xl">🌍</div>
              <div className="absolute bottom-4 left-4 text-5xl">♻️</div>
              <div className="absolute top-1/2 left-8 text-4xl">🌱</div>
              <div className="absolute top-8 left-1/2 text-3xl">✨</div>
            </div>

            <div className="max-w-4xl mx-auto relative z-10">
              <div className="flex items-center justify-center mb-4">
                <div className="bg-gradient-to-r from-green-400 to-emerald-400 rounded-full p-4 mr-4 shadow-lg">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">Our Plastic-Free Promise</h3>
              </div>
              <p className="text-lg md:text-xl mb-6 leading-relaxed text-gray-100">
                Every EcoGrovea product is <strong className="text-green-400 font-bold">100% plastic-free</strong>, ensuring a cleaner planet for future generations.
                We're committed to providing sustainable alternatives that don't compromise on quality or performance.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-center">
                <div className="bg-gradient-to-br from-red-500 to-red-600 rounded-xl p-4 shadow-lg border border-red-400/30 hover:scale-105 transition-transform duration-300">
                  <div className="text-3xl font-bold text-white">0%</div>
                  <div className="text-sm text-red-100">Plastic Content</div>
                </div>
                <div className="bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl p-4 shadow-lg border border-green-400/30 hover:scale-105 transition-transform duration-300">
                  <div className="text-3xl font-bold text-white">100%</div>
                  <div className="text-sm text-green-100">Biodegradable</div>
                </div>
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4 shadow-lg border border-blue-400/30 hover:scale-105 transition-transform duration-300">
                  <div className="text-3xl font-bold text-white">∞</div>
                  <div className="text-sm text-blue-100">Positive Impact</div>
                </div>
              </div>
            </div>
          </div>

          {/* Join Our Community */}
          <div className="mt-10">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold text-gray-900 mb-3 font-playfair">Join Our Growing Community</h3>
              <p className="text-lg text-gray-600">Be a part of the movement towards a sustainable future.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-l-green-500">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <p className="text-gray-700 italic text-lg leading-relaxed">
                      "We believe that every small step towards sustainability can make a big difference. That's why we're committed to providing products that are both eco-friendly and high-quality."
                    </p>
                  </div>
                  <div className="text-center">
                    <h4 className="font-bold text-gray-900 text-lg">The EcoGrovea Team</h4>
                    <p className="text-forest font-medium">Our Commitment to You</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-l-blue-500">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <p className="text-gray-700 italic text-lg leading-relaxed">
                      "Our goal is to make sustainable choices accessible to everyone. We're excited to partner with businesses and individuals who share our passion for protecting the planet."
                    </p>
                  </div>
                  <div className="text-center">
                    <h4 className="font-bold text-gray-900 text-lg">Join the Movement</h4>
                    <p className="text-forest font-medium">Together, We Can Make a Difference</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-l-4 border-l-orange-500">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <p className="text-gray-700 italic text-lg leading-relaxed">
                      "We're just getting started, and we're thrilled to have you on this journey with us. Your support will help us grow and continue to innovate in the sustainable product space."
                    </p>
                  </div>
                  <div className="text-center">
                    <h4 className="font-bold text-gray-900 text-lg">Our Future Vision</h4>
                    <p className="text-forest font-medium">A Greener Tomorrow for All</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
