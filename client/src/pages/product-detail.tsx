import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "wouter";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { ShoppingCart, Star, Minus, Plus, ArrowLeft, Leaf, Recycle, IdCard, MessageSquare } from "lucide-react";
import { useCart } from "@/components/cart-context";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { ProductImageGallery } from "@/components/ui/product-image-gallery";
import { ProductVariationSelector } from "@/components/product-variation-selector";
import type { Product, ProductVariation } from "@shared/schema";

export default function ProductDetail() {
  const { id } = useParams();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariation, setSelectedVariation] = useState<ProductVariation | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [isSubmittingEnquiry, setIsSubmittingEnquiry] = useState(false);
  const { addToCart, items } = useCart();
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  const { data: product, isLoading } = useQuery<Product>({
    queryKey: [`/api/products/${id}`],
    enabled: !!id,
  });

  const handleVariationChange = (variation: ProductVariation | null, quantity: number) => {
    setSelectedVariation(variation);
    setQuantity(quantity);
  };

  const handleAddToCart = async () => {
    if (!product) return;

    // Check if product has variations and one is selected
    if (product.hasVariations && !selectedVariation) {
      toast({
        title: "Please select options",
        description: "Please select all product options before adding to cart.",
        variant: "destructive",
      });
      return;
    }

    setIsAdding(true);
    try {
      await addToCart(product.id, quantity, selectedVariation?.id);
      toast({
        title: "Added to cart!",
        description: `${quantity} ${product.name} added to your enquiry cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to enquiry cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleDirectEnquiry = async () => {
    if (!product) return;

    // Check if product has variations and one is selected
    if (product.hasVariations && !selectedVariation) {
      toast({
        title: "Please select options",
        description: "Please select all product options before submitting enquiry.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmittingEnquiry(true);
    try {
      // Add to cart first, then redirect to checkout
      await addToCart(product.id, quantity, selectedVariation?.id);
      // Redirect to checkout to fill in details
      setLocation("/checkout");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit enquiry. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingEnquiry(false);
    }
  };

  const handleBackToProducts = () => {
    setLocation("/products");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-32 mb-8"></div>
            <div className="grid md:grid-cols-2 gap-12">
              <div className="h-96 bg-gray-200 rounded-lg"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product not found</h1>
          <Button onClick={handleBackToProducts}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Products
          </Button>
        </div>
      </div>
    );
  }

  const hasDiscount = product.originalPrice && parseFloat(product.originalPrice) > parseFloat(product.price);
  const discountPercentage = hasDiscount
    ? Math.round(((parseFloat(product.originalPrice!) - parseFloat(product.price)) / parseFloat(product.originalPrice!)) * 100)
    : 0;

  // Prepare images array for gallery (support multiple images)
  let productImages: string[] = [];

  // If imageUrls exists and has multiple images, use that array
  if (product.imageUrls && Array.isArray(product.imageUrls) && product.imageUrls.length > 0) {
    productImages = product.imageUrls;
  } else if (product.imageUrl) {
    // Fallback to single image
    productImages = [product.imageUrl];
  }

  // Check if product is already in cart (considering variations)
  const isInCart = items.some(item =>
    item.productId === product.id &&
    (selectedVariation ? (item as any).variationId === selectedVariation.id : !(item as any).variationId)
  );



  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <Button variant="ghost" onClick={handleBackToProducts} className="mb-8 p-0">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Products
        </Button>

        <div className="grid md:grid-cols-2 gap-12">
          {/* Product Image Gallery */}
          <div className="relative">
            <ProductImageGallery
              images={productImages}
              productName={product.name}
              className="w-full"
            />
            <div className="absolute top-4 left-4 space-y-2">
              <Badge className="bg-sage text-white">
                <Leaf className="h-3 w-3 mr-1" />
                Eco-Friendly
              </Badge>
              {hasDiscount && (
                <Badge className="bg-terracotta text-white block">
                  -{discountPercentage}% OFF
                </Badge>
              )}
              {product.featured && (
                <Badge className="bg-yellow-500 text-white block">
                  Featured
                </Badge>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-1">
                  <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  <span className="text-lg font-medium">{parseFloat(product.rating).toFixed(1)}</span>
                  <span className="text-gray-500">({product.reviewCount} reviews)</span>
                </div>
                <Badge variant={product.inStock ? "secondary" : "destructive"}>
                  {product.inStock ? "In Stock" : "Out of Stock"}
                </Badge>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-4">
                <span className="text-3xl font-bold text-forest">
                  ₹{parseFloat(product.price).toFixed(2)}
                </span>
                {hasDiscount && (
                  <span className="text-xl text-gray-500 line-through">
                    ₹{parseFloat(product.originalPrice!).toFixed(2)}
                  </span>
                )}
              </div>

              {/* Carton Information */}
              <div className="bg-sage/10 border border-sage/20 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">📦 Carton Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Units per carton:</span>
                    <span className="font-medium ml-2">{(product as any).unitsPerCarton || 1} {(product as any).unitType || 'pieces'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Price per unit:</span>
                    <span className="font-medium text-forest ml-2">
                      ₹{(parseFloat(product.price) / ((product as any).unitsPerCarton || 1)).toFixed(2)}
                    </span>
                  </div>
                  {(product as any).cartonWeight && (
                    <div>
                      <span className="text-gray-600">Carton weight:</span>
                      <span className="font-medium ml-2">{(product as any).cartonWeight} kg</span>
                    </div>
                  )}
                  {(product as any).cartonDimensions && (
                    <div>
                      <span className="text-gray-600">Dimensions:</span>
                      <span className="font-medium ml-2">{(product as any).cartonDimensions}</span>
                    </div>
                  )}
                </div>
              </div>

              {hasDiscount && (
                <p className="text-sm text-terracotta font-medium">
                  Save ₹{(parseFloat(product.originalPrice!) - parseFloat(product.price)).toFixed(2)} ({discountPercentage}% off)
                </p>
              )}
            </div>

            <p className="text-gray-600 text-lg">{product.description}</p>

            {/* Product Variation Selector */}
            {product.hasVariations && (
              <ProductVariationSelector
                productId={product.id}
                onVariationChange={handleVariationChange}
                className="border-t pt-6"
              />
            )}

            {/* Quantity Selector - Only show if no variations */}
            {!product.hasVariations && (
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium">Quantity:</span>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="text-lg font-medium w-12 text-center">{quantity}</span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons - Always show both options */}
            <div className="space-y-4">
              {/* Show selection message if variations exist but none selected */}
              {product.hasVariations && !selectedVariation && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <p className="text-amber-800 text-sm font-medium">
                    Please select product options above to continue
                  </p>
                </div>
              )}

              <div className="space-y-3">
                {/* Primary Action - Add to Cart */}
                <Button
                  className={`w-full py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200 ${
                    isInCart
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-forest hover:bg-forest/90 hover:shadow-xl transform hover:-translate-y-0.5"
                  }`}
                  onClick={handleAddToCart}
                  disabled={
                    isAdding ||
                    !product.inStock ||
                    isInCart ||
                    (product.hasVariations && !selectedVariation)
                  }
                >
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  {isAdding
                    ? "Adding..."
                    : !product.inStock
                      ? "Out of Stock"
                      : isInCart
                        ? "Added to Cart"
                        : product.hasVariations && !selectedVariation
                          ? "Select Options"
                          : `Add ${quantity} to Cart`
                  }
                </Button>

                {/* Secondary Action - Submit Enquiry */}
                <Button
                  variant="outline"
                  className="w-full border-2 border-forest text-forest hover:bg-forest hover:text-white py-4 text-lg font-semibold shadow-md transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5"
                  onClick={handleDirectEnquiry}
                  disabled={
                    isSubmittingEnquiry ||
                    !product.inStock ||
                    (product.hasVariations && !selectedVariation)
                  }
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  {isSubmittingEnquiry
                    ? "Submitting..."
                    : !product.inStock
                      ? "Out of Stock"
                      : product.hasVariations && !selectedVariation
                        ? "Select Options"
                        : "Submit Direct Enquiry"
                  }
                </Button>
              </div>

              {/* Divider */}
              <div className="flex items-center my-4">
                <div className="flex-1 border-t border-gray-200"></div>
                <span className="px-3 text-sm text-gray-500 bg-white">or</span>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>

              {/* Additional info for enquiry */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 rounded-full p-2">
                    <MessageSquare className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-blue-900 font-semibold text-sm">Why Submit an Enquiry?</h4>
                    <ul className="text-blue-700 text-sm mt-2 space-y-1">
                      <li>• Get personalized pricing for bulk orders</li>
                      <li>• Check real-time availability</li>
                      <li>• Receive expert product recommendations</li>
                      <li>• Fast response from our eco-friendly specialists</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Eco Benefits */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Leaf className="h-5 w-5 text-forest" />
                  <span>Eco-Friendly Benefits</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Recycle className="h-5 w-5 text-sage" />
                  <span className="text-sm">100% Compostable in 90-180 days</span>
                </div>
                <div className="flex items-center space-x-3">
                  <IdCard className="h-5 w-5 text-forest" />
                  <span className="text-sm">Certified for commercial composting</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Leaf className="h-5 w-5 text-sage" />
                  <span className="text-sm">Made from renewable plant materials</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Product Specifications */}
        {product.specifications && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Specifications</h2>
            <Card>
              <CardContent className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex justify-between items-center py-2">
                      <span className="font-medium text-gray-700">{key}:</span>
                      <span className="text-gray-900">{value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Product Tags */}
        {product.tags && product.tags.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {product.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-forest border-forest">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
