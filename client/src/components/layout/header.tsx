import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Search, Menu, Shield } from "lucide-react";
import { ShoppingCartDrawer } from "../shopping-cart";
import { Link, useLocation } from "wouter";
import { useAuth } from "../auth-context";
import { AdminOnly, AuthenticatedOnly, GuestOnly } from "../role-based-visibility";

export function Header() {
  const [searchQuery, setSearchQuery] = useState("");
  const [location, setLocation] = useLocation();
  const { isAuthenticated, user, logout, isAdmin } = useAuth();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setLocation(`/products?search=${encodeURIComponent(searchQuery)}`);
      setSearchQuery("");
    }
  };

  const getLinkClass = (path: string, isExact: boolean = false) => {
    const isActive = isExact ? location === path : location.startsWith(path);
    return `py-2 text-white hover:text-yellow-300 font-medium transition-all duration-200 ${
      isActive ? 'border-b-2 border-yellow-300 text-yellow-300' : 'border-b-2 border-transparent'
    }`;
  };

  return (
    <header className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 shadow-lg sticky top-0 z-50 relative backdrop-blur-sm border-b border-green-500/20">
      {/* Subtle decorative overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-green-600/90 via-emerald-600/90 to-teal-600/90"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-8">
            <Link href="/" className={`flex items-center transition-transform transform hover:scale-105 ${location === '/' ? 'scale-105' : ''}`}>
              <img
                src="https://res.cloudinary.com/dlaz5xqrl/image/upload/v1750863482/logo_white_yvjdid.png"
                alt="EcoGrovea Logo"
                className="h-12 object-contain"
                style={{ objectFit: 'contain', objectPosition: 'left center' }}
              />
            </Link>
            
            <nav className="hidden md:flex space-x-6">
              <Link href="/products" className={getLinkClass("/products")}>
                Products
              </Link>
              <Link href="/about" className={getLinkClass("/about", true)}>
                About
              </Link>
              <Link href="/contact" className={getLinkClass("/contact", true)}>
                Contact
              </Link>
            </nav>
          </div>
          
          <div className="flex items-center space-x-4">
            <form onSubmit={handleSearch} className="hidden md:flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/30">
              <Search className="h-4 w-4 text-white mr-2" />
              <Input
                type="text"
                placeholder="Search eco-friendly products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-transparent border-none outline-none w-64 p-0 h-auto focus-visible:ring-0 text-white placeholder:text-white/70"
              />
            </form>

            <ShoppingCartDrawer />

            {/* User Authentication */}
            <AuthenticatedOnly>
              <div className="hidden md:flex items-center space-x-2">
                <span className="text-sm text-white">
                  Hello, {user?.firstName || user?.username}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={logout}
                  className="bg-white text-green-700 hover:bg-green-100"
                >
                  Logout
                </Button>
                <AdminOnly>
                  <Link href="/admin">
                    <Button variant="outline" size="sm" className="bg-white text-green-700 hover:bg-green-100">
                      <Shield className="h-4 w-4 mr-2" />
                      Admin
                    </Button>
                  </Link>
                </AdminOnly>
              </div>
            </AuthenticatedOnly>

            <GuestOnly>
              <div className="hidden md:flex items-center space-x-2">
                <Link href="/login">
                  <Button size="sm" className="bg-white text-green-700 hover:bg-green-100">
                    Login
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm" className="bg-white text-green-700 hover:bg-green-100">
                    Sign Up
                  </Button>
                </Link>
              </div>
            </GuestOnly>

            {/* <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="md:hidden border-white text-white hover:bg-white hover:text-green-700">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger> */}
              {/* <SheetContent side="right">
                <nav className="flex flex-col space-y-4 mt-8">
                  <Link href="/products" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg">
                    Products
                  </Link>
                  <Link href="/about" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg">
                    About
                  </Link>
                  <Link href="/contact" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg">
                    Contact
                  </Link>

                  {/* Mobile Auth Links */}
                  {/* <AuthenticatedOnly>
                    <div className="space-y-2 pt-4 border-t">
                      <div className="text-sm text-gray-600">
                        Hello, {user?.firstName || user?.username}
                      </div>
                      <AdminOnly>
                        <Link href="/admin" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg flex items-center">
                          <Shield className="h-4 w-4 mr-2" />
                          Admin Panel
                        </Link>
                      </AdminOnly>
                      <Button
                        variant="outline"
                        onClick={logout}
                        className="w-full justify-start"
                      >
                        Logout
                      </Button>
                    </div>
                  </AuthenticatedOnly> */}

                  {/* <GuestOnly>
                    <div className="space-y-2 pt-4 border-t">
                      <Link href="/login" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg">
                        Login
                      </Link>
                      <Link href="/register" className="text-gray-700 hover:text-forest font-medium transition-colors text-lg">
                        Sign Up
                      </Link>
                    </div>
                  </GuestOnly>
                  <form onSubmit={handleSearch} className="flex items-center bg-gray-100 rounded-lg px-3 py-2 mt-4">
                    <Search className="h-4 w-4 text-gray-500 mr-2" />
                    <Input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="bg-transparent border-none outline-none w-full p-0 h-auto focus-visible:ring-0"
                    />
                  </form>
                </nav>
              </SheetContent> */}
            {/* </Sheet> */}
          </div>
        </div>
      </div>
    </header>
  );
}
