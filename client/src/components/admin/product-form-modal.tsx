import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { ImageUpload } from "@/components/ui/image-upload";
import { Plus, Trash2 } from "lucide-react";

interface ProductVariation {
  id?: number;
  name: string;
  type: string;
  value: string;
  priceAdjustment: number;
  stockQuantity: number;
  isActive: boolean;
  // Carton information (optional, falls back to product defaults)
  unitsPerCarton?: number;
  unitType?: string;
  cartonWeight?: string;
  cartonDimensions?: string;
}

interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  originalPrice?: string;
  category: string;
  imageUrl: string;
  imageUrls?: string[];
  inStock: boolean;
  featured: boolean;
  stockQuantity?: number;
  rating?: string;
  reviewCount?: number;
  hasVariations?: boolean;
  variations?: ProductVariation[];
}

interface ProductFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  product?: Product | null;
  mode: "add" | "edit";
}

export function ProductFormModal({ 
  isOpen, 
  onClose, 
  onSuccess, 
  product, 
  mode 
}: ProductFormModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    originalPrice: "",
    category: "",
    imageUrl: "",
    imageUrls: [] as string[],
    inStock: true,
    featured: false,
    rating: "4.5",
    reviewCount: "0",
    // Carton information
    unitsPerCarton: "1",
    unitType: "pieces",
    cartonWeight: "",
    cartonDimensions: "",
    // Variations
    hasVariations: false,
  });

  const [variations, setVariations] = useState<ProductVariation[]>([]);

  const categories = [
    { value: "plates", label: "Plates" },
    { value: "containers", label: "Containers" },
    { value: "bowls", label: "Bowls" },
    { value: "cups", label: "Cups" },
    { value: "cutlery", label: "Cutlery" },
    { value: "combo", label: "Combo Packs" },
    { value: "meals tray", label: "Meals Tray" },
  ];

  // Fetch variations for editing
  const fetchProductVariations = async (productId: number) => {
    try {
      const response = await fetch(`/api/products/${productId}/variations`, {
        credentials: "include",
      });
      if (response.ok) {
        const variations = await response.json();
        console.log('📋 Fetched variations:', variations);
        return variations.map((v: any) => ({
          id: v.id,
          name: v.name,
          type: v.type,
          value: v.value,
          priceAdjustment: v.price ? parseFloat(v.price) - parseFloat(product?.price || "0") : 0,
          stockQuantity: v.stockQuantity || 0,
          isActive: v.isActive !== false,
          // Carton information (optional)
          unitsPerCarton: v.unitsPerCarton || undefined,
          unitType: v.unitType || undefined,
          cartonWeight: v.cartonWeight || undefined,
          cartonDimensions: v.cartonDimensions || undefined,
        }));
      }
      return [];
    } catch (error) {
      console.error('❌ Failed to fetch variations:', error);
      return [];
    }
  };

  // Reset form when modal opens/closes or product changes
  useEffect(() => {
    const initializeForm = async () => {
      if (isOpen) {
        if (mode === "edit" && product) {
          setFormData({
            name: product.name,
            description: product.description,
            price: product.price,
            originalPrice: product.originalPrice || "",
            category: product.category,
            imageUrl: product.imageUrl,
            imageUrls: product.imageUrls || [],
            inStock: product.inStock,
            featured: product.featured,
            rating: product.rating || "4.5",
            reviewCount: product.reviewCount?.toString() || "0",
            // Carton information
            unitsPerCarton: (product as any).unitsPerCarton?.toString() || "1",
            unitType: (product as any).unitType || "pieces",
            cartonWeight: (product as any).cartonWeight?.toString() || "",
            cartonDimensions: (product as any).cartonDimensions || "",
            // Variations
            hasVariations: product.hasVariations || false,
          });

          // Fetch existing variations if product has variations
          if (product.hasVariations) {
            console.log('🔧 Product has variations, fetching...');
            const existingVariations = await fetchProductVariations(product.id);
            setVariations(existingVariations);
          } else {
            setVariations([]);
          }
        } else {
          // Reset form for add mode
          setFormData({
            name: "",
            description: "",
            price: "",
            originalPrice: "",
            category: "",
            imageUrl: "",
            imageUrls: [],
            inStock: true,
            featured: false,
            rating: "4.5",
            reviewCount: "0",
            // Carton information
            unitsPerCarton: "1",
            unitType: "pieces",
            cartonWeight: "",
            cartonDimensions: "",
            // Variations
            hasVariations: false,
          });
          setVariations([]);
        }
      }
    };

    initializeForm();
  }, [isOpen, mode, product]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addVariation = () => {
    const newVariation: ProductVariation = {
      name: "",
      type: "size",
      value: "",
      priceAdjustment: 0,
      stockQuantity: 0,
      isActive: true,
      // Initialize carton information as undefined (will use product defaults)
      unitsPerCarton: undefined,
      unitType: undefined,
      cartonWeight: undefined,
      cartonDimensions: undefined,
    };
    console.log('➕ Adding new variation:', newVariation);
    console.log('📝 Current variations before adding:', variations);
    setVariations(prev => {
      const updated = [...prev, newVariation];
      console.log('📝 Updated variations state after adding:', updated);
      console.log('📝 New variations count:', updated.length);
      return updated;
    });
  };

  const updateVariation = (index: number, field: keyof ProductVariation, value: any) => {
    console.log('✏️ Updating variation at index', index, 'field:', field, 'value:', value);
    setVariations(prev => {
      const updated = prev.map((variation, i) =>
        i === index ? { ...variation, [field]: value } : variation
      );
      console.log('📝 Updated variations state:', updated);
      console.log('📝 Updated variation at index', index, ':', updated[index]);
      return updated;
    });
  };

  const removeVariation = (index: number) => {
    console.log('🗑️ Removing variation at index:', index);
    console.log('📝 Current variations before removal:', variations);
    setVariations(prev => {
      const updated = prev.filter((_, i) => i !== index);
      console.log('📝 Updated variations after removal:', updated);
      console.log('📝 New variations count:', updated.length);
      return updated;
    });
  };

  const handleImagesUploaded = (imageUrls: string[], primaryImage: string) => {
    setFormData(prev => ({
      ...prev,
      imageUrls,
      imageUrl: primaryImage
    }));
  };

  const handleImageDeleted = (deletedImageUrl: string) => {
    // Update form data to remove the deleted image
    setFormData(prev => {
      const updatedImageUrls = prev.imageUrls.filter(url => url !== deletedImageUrl);
      return {
        ...prev,
        imageUrls: updatedImageUrls,
        imageUrl: updatedImageUrls.length > 0 ? updatedImageUrls[0] : prev.imageUrl
      };
    });

    toast({
      title: "Image deleted",
      description: "Image has been permanently deleted from cloud storage.",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate that we have at least one image (uploaded via Cloudinary or URL)
      const hasUploadedImages = formData.imageUrls && formData.imageUrls.length > 0;
      const hasImageUrl = formData.imageUrl && formData.imageUrl.trim() !== "";

      if (!hasUploadedImages && !hasImageUrl) {
        toast({
          title: "Image required",
          description: "Please upload at least one image through Cloudinary or provide an image URL.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Validate required fields
      if (!formData.name || !formData.description || !formData.price || !formData.category) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Filter and prepare variations first
      const filteredVariations = formData.hasVariations ? variations.filter(v => v.name && v.value) : [];

      // Remove database IDs from variations for update operations
      const variationsForSubmission = filteredVariations.map(v => ({
        name: v.name,
        type: v.type,
        value: v.value,
        priceAdjustment: v.priceAdjustment,
        stockQuantity: v.stockQuantity,
        isActive: v.isActive,
        // Include carton information (only if provided, otherwise undefined to use product defaults)
        unitsPerCarton: v.unitsPerCarton || undefined,
        unitType: v.unitType || undefined,
        cartonWeight: v.cartonWeight || undefined,
        cartonDimensions: v.cartonDimensions || undefined,
        // Note: We don't include 'id' field for updates
      }));

      console.log('🔧 Variations for submission:', JSON.stringify(variationsForSubmission, null, 2));
      console.log('🔧 Current variations state:', JSON.stringify(variations, null, 2));

      // Validate variations if enabled
      if (formData.hasVariations && variationsForSubmission.length === 0) {
        toast({
          title: "Validation Error",
          description: "Please add at least one variation or disable variations.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Validate that all variations have required fields
      if (formData.hasVariations) {
        const invalidVariations = variationsForSubmission.filter(v => !v.name || !v.value);
        if (invalidVariations.length > 0) {
          toast({
            title: "Validation Error",
            description: "All variations must have a name and value.",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }
      }

      // Ensure we have a valid image URL
      const primaryImageUrl = hasUploadedImages && formData.imageUrls.length > 0
        ? formData.imageUrls[0]
        : formData.imageUrl || "https://via.placeholder.com/400x300?text=No+Image";

      console.log('📝 Form submission - Mode:', mode);
      console.log('📝 Has variations enabled:', formData.hasVariations);
      console.log('📝 Total variations in state:', variations.length);
      console.log('📝 Filtered variations:', filteredVariations.length);
      console.log('📝 Variations for submission:', variationsForSubmission.length);
      console.log('📝 Variations data:', JSON.stringify(variationsForSubmission, null, 2));

      // Prepare data for API
      const apiData = {
        name: formData.name,
        description: formData.description,
        price: formData.price,
        originalPrice: formData.originalPrice || undefined,
        category: formData.category,
        imageUrl: primaryImageUrl,
        imageUrls: hasUploadedImages && formData.imageUrls.length > 0 ? formData.imageUrls : undefined,
        inStock: formData.inStock,
        featured: formData.featured,
        rating: formData.rating || undefined,
        reviewCount: formData.reviewCount ? parseInt(formData.reviewCount) : undefined,
        // Carton information
        unitsPerCarton: parseInt(formData.unitsPerCarton) || 1,
        unitType: formData.unitType || "pieces",
        cartonWeight: formData.cartonWeight || undefined,
        cartonDimensions: formData.cartonDimensions || undefined,
        // Variations
        hasVariations: formData.hasVariations,
        variations: variationsForSubmission,
      };

      console.log('🚀 Final API data being sent:', JSON.stringify(apiData, null, 2));

      const url = mode === "add"
        ? "/api/admin/products"
        : `/api/admin/products/${product?.id}`;

      const method = mode === "add" ? "POST" : "PATCH";

      console.log('🚀 About to send API request:');
      console.log('📍 URL:', url);
      console.log('📍 Method:', method);
      console.log('📍 API Data:', JSON.stringify(apiData, null, 2));

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(apiData),
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response ok:', response.ok);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ API Error:', errorData);
        throw new Error(errorData.message || `Failed to ${mode} product`);
      }

      const responseData = await response.json();
      console.log('✅ API Success:', responseData);

      toast({
        title: `Product ${mode === "add" ? "created" : "updated"}`,
        description: `Product has been successfully ${mode === "add" ? "created" : "updated"}.`,
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${mode} product. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === "add" ? "Add New Product" : "Edit Product"}
          </DialogTitle>
          <DialogDescription>
            {mode === "add" 
              ? "Create a new product for your catalog." 
              : "Update the product information below."
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Product Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter product name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => handleInputChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter product description"
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">Price (₹) *</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => handleInputChange("price", e.target.value)}
                placeholder="0.00"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="originalPrice">Original Price (₹)</Label>
              <Input
                id="originalPrice"
                type="number"
                step="0.01"
                value={formData.originalPrice}
                onChange={(e) => handleInputChange("originalPrice", e.target.value)}
                placeholder="0.00"
              />
            </div>

          </div>

          {/* Carton Information Section */}
          <div className="space-y-4 p-4 border rounded-lg bg-sage/5">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              📦 Carton Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="unitsPerCarton">Units per Carton *</Label>
                <Input
                  id="unitsPerCarton"
                  type="number"
                  min="1"
                  value={formData.unitsPerCarton}
                  onChange={(e) => handleInputChange("unitsPerCarton", e.target.value)}
                  placeholder="1"
                  required
                />
                <p className="text-xs text-gray-500">How many individual units are in one carton</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="unitType">Unit Type *</Label>
                <Select
                  value={formData.unitType}
                  onValueChange={(value) => handleInputChange("unitType", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select unit type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pieces">Pieces</SelectItem>
                    <SelectItem value="plates">Plates</SelectItem>
                    <SelectItem value="bowls">Bowls</SelectItem>
                    <SelectItem value="containers">Containers</SelectItem>
                    <SelectItem value="sets">Sets</SelectItem>
                    <SelectItem value="items">Items</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cartonWeight">Carton Weight (kg)</Label>
                <Input
                  id="cartonWeight"
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.cartonWeight}
                  onChange={(e) => handleInputChange("cartonWeight", e.target.value)}
                  placeholder="2.5"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cartonDimensions">Carton Dimensions</Label>
                <Input
                  id="cartonDimensions"
                  value={formData.cartonDimensions}
                  onChange={(e) => handleInputChange("cartonDimensions", e.target.value)}
                  placeholder="40cm x 30cm x 20cm"
                />
              </div>
            </div>

            {/* Price per unit calculation */}
            {formData.price && formData.unitsPerCarton && (
              <div className="bg-forest/10 border border-forest/20 rounded-lg p-3">
                <p className="text-sm font-medium text-forest">
                  💡 Price per {formData.unitType.slice(0, -1)}: ₹
                  {(parseFloat(formData.price) / parseInt(formData.unitsPerCarton)).toFixed(2)}
                </p>
              </div>
            )}
          </div>

          {/* Product Variations Section */}
          <div className="space-y-4 p-4 border rounded-lg bg-blue-50/50">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                🔧 Product Variations
              </h3>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasVariations"
                  checked={formData.hasVariations}
                  onCheckedChange={(checked) => {
                    handleInputChange("hasVariations", checked);
                    if (!checked) {
                      setVariations([]);
                    }
                  }}
                />
                <Label htmlFor="hasVariations">Enable Variations</Label>
              </div>
            </div>

            {formData.hasVariations && (
              <div className="space-y-4">
                <p className="text-sm text-gray-600">
                  Add size, model, or other variations for this product. Customers will be required to select a variation before adding to cart.
                </p>

                {variations.map((variation, index) => (
                  <div key={index} className="space-y-3 p-4 border rounded-lg bg-white">
                    {/* Basic variation information */}
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Name</Label>
                        <Input
                          placeholder="Small, Large, etc."
                          value={variation.name}
                          onChange={(e) => updateVariation(index, "name", e.target.value)}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Type</Label>
                        <Select
                          value={variation.type}
                          onValueChange={(value) => updateVariation(index, "type", value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="size">Size</SelectItem>
                            <SelectItem value="model">Model</SelectItem>
                            <SelectItem value="color">Color</SelectItem>
                            <SelectItem value="material">Material</SelectItem>
                            <SelectItem value="style">Style</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Value</Label>
                        <Input
                          placeholder="6', 7', A, B, etc."
                          value={variation.value}
                          onChange={(e) => updateVariation(index, "value", e.target.value)}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Price Adj. (₹)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={variation.priceAdjustment}
                          onChange={(e) => updateVariation(index, "priceAdjustment", parseFloat(e.target.value) || 0)}
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Stock</Label>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          value={variation.stockQuantity}
                          onChange={(e) => updateVariation(index, "stockQuantity", parseInt(e.target.value) || 0)}
                        />
                      </div>

                      <div className="flex items-end">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeVariation(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Carton information for this variation */}
                    <div className="border-t pt-3">
                      <div className="flex items-center gap-2 mb-3">
                        <Label className="text-sm font-medium text-gray-700">Carton Information</Label>
                        <span className="text-xs text-gray-500">(Optional - uses product defaults if empty)</span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                        <div className="space-y-1">
                          <Label className="text-xs">Units per Carton</Label>
                          <Input
                            type="number"
                            min="1"
                            placeholder={`Default: ${formData.unitsPerCarton}`}
                            value={variation.unitsPerCarton !== undefined && variation.unitsPerCarton !== null ? variation.unitsPerCarton : formData.unitsPerCarton}
                            onChange={(e) => {
                              const value = e.target.value.trim();
                              updateVariation(index, "unitsPerCarton", value ? parseInt(value) : parseInt(formData.unitsPerCarton));
                            }}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label className="text-xs">Unit Type</Label>
                          <Input
                            placeholder={`Default: ${formData.unitType}`}
                            value={variation.unitType || ""}
                            onChange={(e) => updateVariation(index, "unitType", e.target.value.trim() || undefined)}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label className="text-xs">Carton Weight (kg)</Label>
                          <Input
                            placeholder={`Default: ${formData.cartonWeight || 'Not set'}`}
                            value={variation.cartonWeight || ""}
                            onChange={(e) => updateVariation(index, "cartonWeight", e.target.value.trim() || undefined)}
                          />
                        </div>

                        <div className="space-y-1">
                          <Label className="text-xs">Carton Dimensions</Label>
                          <Input
                            placeholder={`Default: ${formData.cartonDimensions || 'Not set'}`}
                            value={variation.cartonDimensions || ""}
                            onChange={(e) => updateVariation(index, "cartonDimensions", e.target.value.trim() || undefined)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addVariation}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Variation
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      console.log('🔍 DEBUG - Current variations state:', variations);
                      console.log('🔍 DEBUG - Has variations enabled:', formData.hasVariations);
                      console.log('🔍 DEBUG - Variations count:', variations.length);
                    }}
                  >
                    Debug
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Cloudinary Image Upload Section */}
          <ImageUpload
            key={`image-upload-${mode}-${product?.id || 'new'}`} // Force re-render when product changes
            onImagesUploaded={handleImagesUploaded}
            maxImages={3}
            existingImages={formData.imageUrls}
            autoCompress={true} // Enable compression for optimal performance
            showDeleteFromCloud={mode === "edit"} // Only show delete option when editing
            onImageDeleted={handleImageDeleted}
          />

          {/* Alternative Image URL (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="imageUrl">Alternative Image URL (Optional)</Label>
            <Input
              id="imageUrl"
              type="url"
              value={formData.imageUrl}
              onChange={(e) => handleInputChange("imageUrl", e.target.value)}
              placeholder="https://example.com/image.jpg (if not using Cloudinary upload)"
            />
            <p className="text-sm text-gray-500">
              💡 <strong>Recommended:</strong> Use the Cloudinary upload above for automatic optimization and global CDN delivery.
              This field is only needed if you want to use an external image URL instead.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="rating">Rating</Label>
              <Input
                id="rating"
                type="number"
                step="0.1"
                min="0"
                max="5"
                value={formData.rating}
                onChange={(e) => handleInputChange("rating", e.target.value)}
                placeholder="4.5"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reviewCount">Review Count</Label>
              <Input
                id="reviewCount"
                type="number"
                value={formData.reviewCount}
                onChange={(e) => handleInputChange("reviewCount", e.target.value)}
                placeholder="0"
              />
            </div>
          </div>



          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="inStock"
                checked={formData.inStock}
                onCheckedChange={(checked) => handleInputChange("inStock", checked)}
              />
              <Label htmlFor="inStock">In Stock</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) => handleInputChange("featured", checked)}
              />
              <Label htmlFor="featured">Featured Product</Label>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading 
                ? `${mode === "add" ? "Creating" : "Updating"}...` 
                : `${mode === "add" ? "Create" : "Update"} Product`
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
