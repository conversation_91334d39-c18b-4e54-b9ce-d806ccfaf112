// CSRF token management utility

let csrfToken: string | null = null;

/**
 * Fetch CSRF token from the server
 */
export async function fetchCSRFToken(): Promise<string> {
  try {
    const response = await fetch('/api/auth/csrf-token', {
      method: 'GET',
      credentials: 'include',
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch CSRF token');
    }
    
    const data = await response.json();
    csrfToken = data.csrfToken;
    return csrfToken;
  } catch (error) {
    console.error('Error fetching CSRF token:', error);
    throw error;
  }
}

/**
 * Get the current CSRF token, fetch if not available
 */
export async function getCSRFToken(): Promise<string> {
  if (!csrfToken) {
    return await fetchCSRFToken();
  }
  return csrfToken;
}

/**
 * Clear the stored CSRF token (useful when it becomes invalid)
 */
export function clearCSRFToken(): void {
  csrfToken = null;
}

/**
 * Enhanced fetch function that automatically includes CSRF token
 */
export async function fetchWithCSRF(url: string, options: RequestInit = {}): Promise<Response> {
  const method = options.method?.toUpperCase() || 'GET';
  
  // Only add CSRF token for state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
    try {
      const token = await getCSRFToken();
      
      // Add CSRF token to headers
      const headers = new Headers(options.headers);
      headers.set('X-CSRF-Token', token);
      
      options.headers = headers;
    } catch (error) {
      console.error('Failed to get CSRF token:', error);
      // Continue with request without CSRF token
    }
  }
  
  // Ensure credentials are included
  options.credentials = options.credentials || 'include';
  
  const response = await fetch(url, options);
  
  // If we get a CSRF error, clear the token and retry once
  if (response.status === 403) {
    try {
      const errorData = await response.clone().json();
      if (errorData.code === 'CSRF_TOKEN_INVALID') {
        clearCSRFToken();
        
        // Retry once with fresh token
        if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
          const newToken = await fetchCSRFToken();
          const headers = new Headers(options.headers);
          headers.set('X-CSRF-Token', newToken);
          options.headers = headers;
          
          return fetch(url, options);
        }
      }
    } catch (e) {
      // If we can't parse the error, just return the original response
    }
  }
  
  return response;
}

/**
 * Initialize CSRF token on app startup
 */
export async function initializeCSRF(): Promise<void> {
  try {
    await fetchCSRFToken();
    console.log('CSRF token initialized');
  } catch (error) {
    console.warn('Failed to initialize CSRF token:', error);
  }
}
