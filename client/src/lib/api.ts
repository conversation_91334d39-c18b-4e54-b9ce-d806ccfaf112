// API client with CSRF protection

import { fetchWithCSRF } from './csrf';

/**
 * API client class with CSRF protection
 */
class APIClient {
  private baseURL: string;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
  }

  /**
   * Make a GET request
   */
  async get<T>(endpoint: string): Promise<T> {
    const response = await fetchWithCSRF(`${this.baseURL}${endpoint}`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`GET ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Make a POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetchWithCSRF(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`POST ${endpoint} failed: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Make a PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetchWithCSRF(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`PUT ${endpoint} failed: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetchWithCSRF(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DELETE ${endpoint} failed: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * Upload files with CSRF protection
   */
  async uploadFiles<T>(endpoint: string, formData: FormData): Promise<T> {
    const response = await fetchWithCSRF(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header for FormData, let browser set it with boundary
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`UPLOAD ${endpoint} failed: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }
}

// Export a singleton instance
export const api = new APIClient();

// Export the class for custom instances
export { APIClient };
